// .footerBlock {
//     margin: calc(var(--gap-padding) * 2) 0;
//   }
  

// .footerBlocks {
//     display: flex;
//     flex-direction: column; // ou row selon ta mise en page
//     gap: calc(var(--gap-padding) * 2);
//     /* Pas de margin sur les .footerBlock ici */
//   }

@import "@/styles/colors.scss";

.footerContainer {
    padding-top: 15rem;
}


.footerBlocks {
    display: flex;
    flex-direction: column; // ou row selon ta mise en page
    gap: calc(var(--gap-padding) * 2);
  }
  
  


.footerLegal {
    //    display: flex; 
    gap: 1rem;
    display: flex;
    flex-direction: column-reverse;
    margin-top: var(--gap-padding);

        a, p, small {
            text-transform: initial !important;

        }
        
    .footerLegalLinks {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        color: $color-white-gray;
    }
    }



.logoTrigger {
    height: 1px;  // ou 5px, selon tes besoins
    width: 100%;
    opacity: 0;
    pointer-events: none;
  }

// .footerBlocks {
//   display: flex;
//   flex-direction: column; // ou row si c'est en ligne
//   // Supprime les marges globales
// }

// .footerBlock:not(:first-child) {
//   margin-top: calc(var(--gap-padding) * 2);
// }

// .footerBlock:not(:last-child) {
//   margin-bottom: calc(var(--gap-padding) * 2);
// }

  
   
   .footer {
        background: $color-dark-brown;
        color: $color-white-gray;
        padding-top: var(--section-padding);
        padding-bottom: var(--section-padding);

        .footerLogo {
            font-size: calc(3.2rem + 4vw);
            font-weight: 500;
            letter-spacing: calc(-0.5px - 1vw);
            word-spacing: calc(5px + 0.5vw);
        }

        // .footerBlock {
        //     margin: calc(var(--gap-padding) * 2) 0;
        // }

        ul {
            list-style: none;
            padding-left: 0;
        }



        .infoBlockTitle {
            margin-top: 0;
        }
    }




    @media screen and (min-width:540px) {
        .footerBlocks {
            display: flex;
            gap: var(--gap-padding);
            flex-direction: row;
        }

        .footerBlock {
            margin: 0;
        }

        .footerLogo {
            font-size: calc(4.2rem + 4vw) !important;
        }

        .footerLegal {
            flex-direction: column !important;
            margin-top: calc(var(--gap-padding) * 3);

            .footerLegalLinks {
                flex-direction: row !important;
            }
        }

        .footerLogo {
            margin-bottom: 5rem;
            display: block;
        }

        .footer {
            padding-top: calc(var(--section-padding) / 2);
            padding-bottom: calc(var(--section-padding) / 2);
        }
    }

    @media screen and (min-width:768px) {
        .footerSection {
            display: flex;
            gap: var(--gap-padding);
        }

        .footerLogo {
            font-size: calc(6.2rem + 5vw) !important;
        }
    }

    @media screen and (min-width:992px) {
        .footerLegal {
            flex-direction: row !important;
            justify-content: space-between;
            // margin-bottom: 2rem !important;
        }

        .footerSection {
            justify-content: space-between;
        }


        .footer {
            padding-bottom: 1rem;
        }

        .footerLogo {
            font-size: calc(7.2rem + 6vw) !important;
            letter-spacing: calc(-1px - 1.4vw) !important;
        }

        .footerContainer {
            padding-top: var(--section-padding);
        }


    }



    /* components/Footer/style.module.scss */

    .footerContainer {
        position: relative;
        height: 900px; // hauteur du footer, à adapter selon votre design
        overflow: hidden;
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
    
    .innerContainer {
        position: relative;
        height: calc(100vh + 900px); // 100vh + hauteur du footer
        top: -100vh; // décale vers le haut pour que le footer soit caché initialement
    }
    
    .stickyContent {
        position: sticky;
        top: 0;
        // height: 800px;
        background: $color-dark-brown;
        color: $color-white-gray;
        padding-top: var(--section-padding);
        padding-bottom: calc(var(--section-padding) / 2);
    }


    .footerBlocks {
        ul {
            list-style: none;
            padding-left: 0;
        }
    }







// .contact{
//     color: white;
//     display: flex;
//     flex-direction: column;
//     align-items: center;
//     justify-content: center;
//     background-color: #141516;
//     position: relative;
//     .body{
//         padding-top: 200px;
//         width: 100%;
//         max-width: 1800px;
//         background-color: #141516;

//         .title{
//             border-bottom: 1px solid rgb(134, 134, 134);
//             padding-bottom: 100px;
//             margin-left: 200px;
//             margin-right: 200px;
//             position: relative;
//             span{
//                 display: flex;
//                 align-items: center;
//                 .imageContainer{
//                     width: 100px;
//                     height: 100px;
//                     position: relative;
//                     border-radius: 50%;
//                     overflow: hidden;

//                     img{
//                         object-fit: cover;
//                     }
//                 }
//                 h2{
//                     margin-left: 0.3em;
//                 }
//             }
//             h2{
//                 font-size: 5vw;
//                 margin: 0px;
//                 font-weight: 300;
//             }
//             .buttonContainer{
//                 position: absolute;
//                 left: calc(100% - 400px); 
//                 top: calc(100% - 75px);
//             }
//             .button{
//                 width: 180px;
//                 height: 180px;
//                 background-color: #455CE9;
//                 color: white;
//                 border-radius: 50%;
//                 position: absolute;
//                 display: flex;
//                 align-items: center;
//                 justify-content: center;
//                 cursor: pointer;
//                 p{
//                     margin: 0px;
//                     font-size: 16px;
//                     font-weight: 300;
//                     z-index: 2;
//                     position: relative;
//                 }
//             }
//             svg{
//                 position: absolute;
//                 top: 30%;
//                 left: 100%;
//             }
//         }
//         .nav{
//             display: flex;
//             gap: 20px;
//             margin-top: 100px;
//             margin-left: 200px;
//             margin-right: 200px;
//         }
//         .info{
//             display: flex;
//             justify-content: space-between;
//             margin-top: 200px;
//             padding: 20px;

//             .infoBlock {

//                 .infoBlockTitle {
//                     margin-bottom: 1rem;
//                 }
//             }




//             div{
//                 display: flex;
//                 gap: 10px;
//                 align-items: flex-end;
//                 p, h3{
//                     margin: 0px;
//                     padding: 2.5px;
//                     cursor: pointer;
//                 }
//                 p{
//                     &::after{
//                         content: "";
//                         width: 0%;
//                         height: 1px;
//                         background-color: white;
//                         display: block;
//                         margin-top: 2px;
//                         position: relative;
//                         left: 50%;
//                         transform: translateX(-50%);
//                         transition: width 0.2s linear;
//                     }
//                     &:hover{
//                         &::after{
//                             width: 100%;
//                         }
//                     }
//                 }
//                 span{
//                     display: flex;
//                     flex-direction: column;
//                     // gap: 15px;
//                 }
//                 h3{
//                     color: grey;
//                     cursor: default;
//                     font-weight: 300;
//                     font-size: 1em;
//                 }
//             }
            
//         }
//     }
    
// }
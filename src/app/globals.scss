@import "../styles/typography.scss";
@import "../styles/variables.scss";
@import "../styles/colors.scss";
@import "material-symbols";


.material-symbols-outlined {
  font-variation-settings:
  'FILL' 0,
  'wght' 200,
  'GRAD' 0,
  'opsz' 40;
  font-size: 40px;
}

::selection {
  background: var(--selection-color, #ff0000);
  color: $color-white;
}



html, body {
  margin: 0px;
  padding: 0;
  overflow-x: hidden; /* Empêche le scroll horizontal */
  cursor: wait;
  background: $color-white;
  width: 100%;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: border-box;
}

main {
  background: $color-white;
}

:root {
  --container-padding: clamp(2.5em, 8vw, 8em);
  --section-padding: clamp(5em, 21vh, 12em);
  --gap-padding: clamp(1.5em, 4vw, 2.5em);
  --section-padding: clamp(5em, 21vh, 12em);
}

@media screen and (max-width: 540px){

  :root { 
      --container-padding: clamp(1.25em, 4vw, 2.5em);
      --section-padding: max(2.5em, 12vh);
  }

  .container.small {
    padding-left: var(--container-padding) !important;
    padding-right: var(--container-padding) !important;
  }
}

.section {
  padding-top: var(--section-padding);
  padding-bottom: var(--section-padding);
} 

.container {
  margin: 0 auto;
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
  max-width: 100em;
}

.container.large {
  padding-left: var(--gap-padding);
  padding-right: var(--gap-padding);
}

.container.medium {
  padding-left: calc(var(--container-padding) * 2);
  padding-right: calc(var(--container-padding) * 2);
}

.container.small {
  padding-left: calc(var(--container-padding) * 4);
  padding-right: calc(var(--container-padding) * 4);
}

.container.no-padding {
  padding-left: unset;
  padding-right: unset;
}

.container.left {
  padding-left: var(--container-padding);
  padding-right: calc(var(--container-padding) * 6);
}


.default-hero {
  padding-top: calc(var(--section-padding)* 1.33);
  padding-bottom: calc(var(--section-padding)* .66);
  background: $color-white;
}











// CSS global pour éviter les conflits avec les composants modulaires